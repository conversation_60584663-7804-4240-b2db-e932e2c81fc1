#!/usr/bin/env python3
# -*- coding: utf-8 -*-


# 导入必要的模块
from Product import *    # 导入商品类
from StockIo import *   # 导入数据导入导出类
import copy             # 导入深拷贝模块


class Stock(object):
    """
    库存管理类
    负责管理商品库存的增删改查等操作
    提供库存统计、查询和显示功能
    """

    def __init__(self):
        """
        构造函数
        初始化商品列表为空列表
        """
        # 商品列表，私有属性
        self.__goods = []
    
    def intoStock(self, product):
        """
        商品入库操作
        参数：
        - product: 要入库的商品对象
        处理逻辑：
        1. 如果库存中已有该商品（通过index判断），则增加数量
        2. 如果是新商品，则添加到库存列表
        """
        # 检查库存是否为空
        if len(self.__goods) > 0:
            flag = False
            # 查找是否存在相同商品
            for good in self.__goods:
                if good.index == product.index:
                    good.number += product.number
                    flag = True
                    break
            # 如果是新商品，添加到列表
            if not flag:
                self.__goods.append(product)
        # 如果库存为空，直接添加
        else:
            self.__goods.append(product)
    
    def searchGood(self, product, print_flag=True):
        """
        查找商品
        参数：
        - product: 包含查找条件的商品对象
        - print_flag: 是否打印查找结果，默认为True
        返回值：
        - 查找到的商品列表
        查找方式：
        1. 通过商品ID（index）查找
        2. 通过商品名称（name）查找
        """
        search_result_list = []    # 存储查找结果

        # 根据输入条件查找商品
        if (product.name == "" and product.index != ""):
            # 通过ID查找
            for good in self.__goods:
                if good.index == product.index:
                    search_result_list.append(good)
                    break
        elif (product.name != "" and product.index == ""):
            # 通过名称查找
            for good in self.__goods:
                if good.name == product.name:
                    search_result_list.append(good)

        # 打印查找结果
        if (print_flag):
            print("===============================================================")
            print("                            查询商品列表                         ")
            print("===============================================================")
            if (len(search_result_list) == 0):
                print("ROBOT：该商品不在库中！")
            else:
                for good in search_result_list:
                    good.formatPrint()
            print("---------------------------------------------------------------\n")

        return search_result_list
    
    def outOfStock(self, product):
        """
        商品出库操作
        参数：
        - product: 要出库的商品信息
        处理逻辑：
        1. 查找要出库的商品
        2. 如果找到多个商品，让用户选择
        3. 检查库存是否充足
        4. 执行出库操作并更新库存
        """
        search_idx_list = []    # 存储查找到的商品索引

        # 查找要出库的商品
        if (product.name == "" and product.index != ""):
            # 通过ID查找
            for i, good in enumerate(self.__goods):
                if good.index == product.index:
                    search_idx_list.append(i)
                    break
        elif(product.name != "" and product.index == ""):
            # 通过名称查找
            for i, good in enumerate(self.__goods):
                if good.name == product.name:
                    search_idx_list.append(i)

        # 处理查找结果
        selected_idx = 0
        if(len(search_idx_list) == 0):
            print("ROBOT：该商品不在库中！")
            return
        elif(len(search_idx_list) > 1):
            # 如果找到多个商品，让用户选择
            print("找到多个同名商品，请选择：")
            for i, idx in enumerate(search_idx_list):
                print(f"{i+1}. ", end="")
                self.__goods[idx].formatPrint()
            try:
                choice = int(input("请输入选择的商品编号: ")) - 1
                if 0 <= choice < len(search_idx_list):
                    selected_idx = search_idx_list[choice]
                else:
                    print("选择无效！")
                    return
            except ValueError:
                print("输入无效！")
                return
        else:
            selected_idx = search_idx_list[0]

        # 执行出库操作
        if (self.__goods[selected_idx].number >= product.number):
            # 库存充足，执行出库
            self.__goods[selected_idx].number -= product.number
            print(f"出库成功！商品 {self.__goods[selected_idx].name} 出库 {product.number} 件")
        else:
            # 库存不足，显示错误信息
            print(f"库存不足！当前库存：{self.__goods[selected_idx].number}，请求出库：{product.number}")

    def listAllGoods(self):
        """
        显示所有商品列表
        功能：格式化打印所有商品的详细信息
        """
        print("===============================================================")
        print("                            所有商品列表                         ")
        print("===============================================================")
        for good in self.__goods:
            good.formatPrint()
        print("---------------------------------------------------------------\n")

    def statisticGoods(self, min_number=15):
        """
        统计商品信息
        参数：
        - min_number: 库存告警阈值，默认为15
        统计内容：
        1. 商品种类总数
        2. 商品总数量
        3. 商品总价值
        4. 库存不足商品列表
        """
        total_number = 0    # 商品总数量
        total_price = 0     # 商品总价值
        selected_list = []  # 库存告急商品列表

        # 统计信息
        for good in self.__goods:
            total_number += good.number
            total_price += good.number * good.price
            if(good.number < min_number):
                selected_list.append(good)

        # 打印统计结果
        print("===============================================================")
        print("                            商品统计信息                         ")
        print("===============================================================")
        print('总计： 商品种类：%d | 商品总数：%d | 商品总价：%f | 库存不足商品种类：%d' % (
            len(self.__goods), total_number, total_price, len(selected_list)))
        print("---------------------------------------------------------------")
        print("                          库存不足商品列表                       ")
        for good in selected_list:
            good.formatPrint()
        print("---------------------------------------------------------------\n")

    def getGoods(self):
        """
        返回商品数据

        Returns:
            list: 商品列表
        """
        return self.goods

    def setGoods(self, goods):
        """
        设置商品数据

        Args:
            goods (list): 商品列表
        """
        if isinstance(goods, list):
            self.goods = goods
            print(f"成功设置 {len(goods)} 个商品")
        else:
            print("错误：输入的不是有效的商品列表")

    def clearStock(self):
        """
        清空库存
        """
        self.goods.clear()
        print("库存已清空")

    def getTotalValue(self):
        """
        获取库存总价值

        Returns:
            float: 库存总价值
        """
        return sum(good.getTotalValue() for good in self.goods)
