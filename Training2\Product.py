#!/usr/bin/env python3
# -*- coding: utf-8 -*-


class Product:
    """
    商品类
    用于管理单个商品的基本信息，包括名称、价格、数量和序列号
    提供属性的设置方法和格式化输出功能
    """

    def __init__(self, name="", price=0.0, number=0, index=""):
        """
        构造函数（支持无参和带参调用）

        无参调用时：Product() - 初始化所有属性为默认值
        带参调用时：Product(name, price, number, index) - 初始化为指定值

        参数说明：
        - name: 商品名称，默认为空字符串
        - price: 商品价格，默认为0.0
        - number: 商品数量，默认为0
        - index: 商品序列号，默认为空字符串
        """
        # 设置商品名称
        self.name = name
        # 设置商品价格
        self.price = price
        # 设置商品数量
        self.number = number
        # 设置商品序列号
        self.index = index
    
    def setName(self, name):
        """
        设置商品名称
        参数：
        - name: 新的商品名称
        """
        self.name = name

    def setNumber(self, number):
        """
        设置商品数量
        参数：
        - number: 新的商品数量
        """
        self.number = number

    def setPrice(self, price):
        """
        设置商品价格
        参数：
        - price: 新的商品价格
        """
        self.price = price

    def setIndex(self, index):
        """
        设置商品序列号
        参数：
        - index: 新的商品序列号
        """
        self.index = index
    
    def formatPrint(self):
        """
        格式化打印商品信息
        输出格式：产品ID：xxx | 产品名称：xxx | 产品剩余数量：xxx | 产品价格：xxx
        """
        print('产品ID：%s | 产品名称：%s | 产品剩余数量：%d | 产品价格：%f' % (
            self.index, self.name, self.number, self.price))
    
    def __str__(self):
        """
        返回商品的字符串表示

        Returns:
            str: 商品信息字符串
        """
        return '产品ID：%s | 产品名称：%s | 产品剩余数量：%d | 产品价格：%f' % (
            self.index, self.name, self.number, self.price)
    
    def __repr__(self):
        """
        返回商品的详细字符串表示
        
        Returns:
            str: 商品详细信息字符串
        """
        return f"Product(name='{self.name}', price={self.price}, number={self.number}, index='{self.index}')"
    
    def getTotalValue(self):
        """
        计算商品总价值（价格 × 数量）
        
        Returns:
            float: 商品总价值
        """
        return self.price * self.number
    
    def isValidProduct(self):
        """
        检查商品信息是否有效
        
        Returns:
            bool: 如果商品信息有效返回True，否则返回False
        """
        return (self.name != "" and 
                self.price >= 0 and 
                self.number >= 0 and 
                self.index != "")
