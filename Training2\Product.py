class Product:
    """
    商品类，用于表示库存管理系统中的商品
    """
    
    def __init__(self, name="", price=0.0, number=0, index=""):
        """
        初始化商品对象
        
        Args:
            name (str): 商品名称，默认为空字符串
            price (float): 商品价格，默认为0.0
            number (int): 商品数量，默认为0
            index (str): 商品序列号，默认为空字符串
        """
        self.name = name
        self.price = price
        self.number = number
        self.index = index
    
    def setName(self, name):
        """
        设置商品名称
        
        Args:
            name (str): 商品名称
        """
        self.name = name
    
    def setNumber(self, number):
        """
        设置商品数量
        
        Args:
            number (int): 商品数量
        """
        self.number = number
    
    def setPrice(self, price):
        """
        设置商品价格
        
        Args:
            price (float): 商品价格
        """
        self.price = price
    
    def setIndex(self, index):
        """
        设置商品序列号
        
        Args:
            index (str): 商品序列号
        """
        self.index = index
    
    def formatPrint(self):
        """
        格式化输出商品信息
        
        Returns:
            str: 格式化的商品信息字符串
        """
        return f"商品名称: {self.name}, 价格: {self.price:.2f}, 数量: {self.number}, 序列号: {self.index}"
    
    def __str__(self):
        """
        返回商品的字符串表示
        
        Returns:
            str: 商品信息字符串
        """
        return self.formatPrint()
    
    def __repr__(self):
        """
        返回商品的详细字符串表示
        
        Returns:
            str: 商品详细信息字符串
        """
        return f"Product(name='{self.name}', price={self.price}, number={self.number}, index='{self.index}')"
    
    def getTotalValue(self):
        """
        计算商品总价值（价格 × 数量）
        
        Returns:
            float: 商品总价值
        """
        return self.price * self.number
    
    def isValidProduct(self):
        """
        检查商品信息是否有效
        
        Returns:
            bool: 如果商品信息有效返回True，否则返回False
        """
        return (self.name != "" and 
                self.price >= 0 and 
                self.number >= 0 and 
                self.index != "")
