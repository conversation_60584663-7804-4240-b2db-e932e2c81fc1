import os
import json
from Product import Product


class StockIo:
    """
    库存输入输出类，用于处理商品数据的文件和Excel导入导出
    """
    
    def __init__(self):
        """
        初始化StockIo对象
        """
        pass
    
    def export_to_file(self, goods, file_path="goods_data.txt"):
        """
        导出商品列表至文件
        
        Args:
            goods (list): 商品列表
            file_path (str): 文件路径，默认为"goods_data.txt"
            
        Returns:
            bool: 导出成功返回True，失败返回False
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write("商品名称\t价格\t数量\t序列号\n")
                for good in goods:
                    file.write(f"{good.name}\t{good.price}\t{good.number}\t{good.index}\n")
            
            print(f"商品列表已成功导出到文件: {file_path}")
            print(f"共导出 {len(goods)} 个商品")
            return True
            
        except Exception as e:
            print(f"导出文件失败: {str(e)}")
            return False
    
    def import_from_file(self, file_path, stock):
        """
        从文件导入商品列表
        
        Args:
            file_path (str): 文件路径
            stock (Stock): 库存管理对象
            
        Returns:
            bool: 导入成功返回True，失败返回False
        """
        if not os.path.exists(file_path):
            print(f"错误：文件 {file_path} 不存在")
            return False
        
        try:
            imported_goods = []
            with open(file_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()
                
                # 跳过标题行
                for line in lines[1:]:
                    line = line.strip()
                    if line:
                        parts = line.split('\t')
                        if len(parts) >= 4:
                            name = parts[0]
                            price = float(parts[1])
                            number = int(parts[2])
                            index = parts[3]
                            
                            product = Product(name, price, number, index)
                            imported_goods.append(product)
            
            # 将导入的商品添加到库存中
            for product in imported_goods:
                stock.intoStock(product)
            
            print(f"从文件 {file_path} 成功导入 {len(imported_goods)} 个商品")
            return True
            
        except Exception as e:
            print(f"导入文件失败: {str(e)}")
            return False
    
    def export_to_excel(self, goods, file_path="goods_data.xls"):
        """
        导出商品列表至Excel文件
        
        Args:
            goods (list): 商品列表
            file_path (str): Excel文件路径，默认为"goods_data.xls"
            
        Returns:
            bool: 导出成功返回True，失败返回False
        """
        try:
            import xlwt
            
            # 创建工作簿和工作表
            workbook = xlwt.Workbook()
            worksheet = workbook.add_sheet('商品列表')
            
            # 设置标题样式
            title_style = xlwt.XFStyle()
            font = xlwt.Font()
            font.bold = True
            title_style.font = font
            
            # 写入标题行
            headers = ['商品名称', '价格', '数量', '序列号', '总价值']
            for col, header in enumerate(headers):
                worksheet.write(0, col, header, title_style)
            
            # 写入数据行
            for row, good in enumerate(goods, 1):
                worksheet.write(row, 0, good.name)
                worksheet.write(row, 1, good.price)
                worksheet.write(row, 2, good.number)
                worksheet.write(row, 3, good.index)
                worksheet.write(row, 4, good.getTotalValue())
            
            # 保存文件
            workbook.save(file_path)
            
            print(f"商品列表已成功导出到Excel文件: {file_path}")
            print(f"共导出 {len(goods)} 个商品")
            return True
            
        except ImportError:
            print("错误：未安装xlwt库，请先安装: pip install xlwt")
            return False
        except Exception as e:
            print(f"导出Excel文件失败: {str(e)}")
            return False

    def import_from_excel(self, file_path, stock):
        """
        从Excel文件导入商品列表

        Args:
            file_path (str): Excel文件路径
            stock (Stock): 库存管理对象

        Returns:
            bool: 导入成功返回True，失败返回False
        """
        if not os.path.exists(file_path):
            print(f"错误：文件 {file_path} 不存在")
            return False

        try:
            import xlrd

            # 打开Excel文件
            workbook = xlrd.open_workbook(file_path)
            worksheet = workbook.sheet_by_index(0)  # 读取第一个工作表

            imported_goods = []

            # 从第二行开始读取数据（跳过标题行）
            for row in range(1, worksheet.nrows):
                try:
                    name = str(worksheet.cell_value(row, 0)).strip()
                    price = float(worksheet.cell_value(row, 1))
                    number = int(worksheet.cell_value(row, 2))
                    index = str(worksheet.cell_value(row, 3)).strip()

                    if name and index:  # 确保名称和序列号不为空
                        product = Product(name, price, number, index)
                        imported_goods.append(product)

                except (ValueError, IndexError) as e:
                    print(f"警告：第 {row + 1} 行数据格式错误，已跳过: {str(e)}")
                    continue

            # 将导入的商品添加到库存中
            success_count = 0
            for product in imported_goods:
                if stock.intoStock(product):
                    success_count += 1

            print(f"从Excel文件 {file_path} 成功导入 {success_count} 个商品")
            return True

        except ImportError:
            print("错误：未安装xlrd库，请先安装: pip install xlrd")
            return False
        except Exception as e:
            print(f"导入Excel文件失败: {str(e)}")
            return False

    def export_to_json(self, goods, file_path="goods_data.json"):
        """
        导出商品列表至JSON文件

        Args:
            goods (list): 商品列表
            file_path (str): JSON文件路径，默认为"goods_data.json"

        Returns:
            bool: 导出成功返回True，失败返回False
        """
        try:
            goods_data = []
            for good in goods:
                goods_data.append({
                    'name': good.name,
                    'price': good.price,
                    'number': good.number,
                    'index': good.index
                })

            with open(file_path, 'w', encoding='utf-8') as file:
                json.dump(goods_data, file, ensure_ascii=False, indent=2)

            print(f"商品列表已成功导出到JSON文件: {file_path}")
            print(f"共导出 {len(goods)} 个商品")
            return True

        except Exception as e:
            print(f"导出JSON文件失败: {str(e)}")
            return False

    def import_from_json(self, file_path, stock):
        """
        从JSON文件导入商品列表

        Args:
            file_path (str): JSON文件路径
            stock (Stock): 库存管理对象

        Returns:
            bool: 导入成功返回True，失败返回False
        """
        if not os.path.exists(file_path):
            print(f"错误：文件 {file_path} 不存在")
            return False

        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                goods_data = json.load(file)

            imported_goods = []
            for item in goods_data:
                product = Product(
                    item.get('name', ''),
                    item.get('price', 0.0),
                    item.get('number', 0),
                    item.get('index', '')
                )
                imported_goods.append(product)

            # 将导入的商品添加到库存中
            success_count = 0
            for product in imported_goods:
                if stock.intoStock(product):
                    success_count += 1

            print(f"从JSON文件 {file_path} 成功导入 {success_count} 个商品")
            return True

        except Exception as e:
            print(f"导入JSON文件失败: {str(e)}")
            return False
