from Product import Product


class Stock:
    """
    库存管理类，用于管理商品的入库、出库、查询等操作
    """
    
    def __init__(self):
        """
        初始化库存管理对象
        """
        self.goods = []  # 存储所有商品的列表
    
    def intoStock(self, product):
        """
        商品入库操作
        
        Args:
            product (Product): 要入库的商品对象
            
        Returns:
            bool: 入库成功返回True，失败返回False
        """
        if not isinstance(product, Product):
            print("错误：输入的不是有效的商品对象")
            return False
        
        if not product.isValidProduct():
            print("错误：商品信息不完整")
            return False
        
        # 检查是否已存在相同序列号的商品
        for existing_product in self.goods:
            if existing_product.index == product.index:
                print(f"警告：序列号 {product.index} 已存在，将增加数量")
                existing_product.number += product.number
                return True
        
        # 如果不存在相同序列号，直接添加新商品
        self.goods.append(product)
        print(f"商品 {product.name} 入库成功")
        return True
    
    def searchGood(self, product, print_flag=True):
        """
        查找商品
        
        Args:
            product (Product): 包含查询条件的商品对象
            print_flag (bool): 是否打印查询结果，默认为True
            
        Returns:
            list: 匹配的商品列表
        """
        found_products = []
        
        # 如果提供了序列号，优先按序列号查找
        if product.index:
            for good in self.goods:
                if good.index == product.index:
                    found_products.append(good)
                    break
        # 否则按商品名称查找
        elif product.name:
            for good in self.goods:
                if good.name == product.name:
                    found_products.append(good)
        
        if print_flag:
            if found_products:
                print("查询结果：")
                for i, good in enumerate(found_products, 1):
                    print(f"{i}. {good.formatPrint()}")
            else:
                print("未找到匹配的商品")
        
        return found_products
    
    def outOfStock(self, product):
        """
        商品出库操作
        
        Args:
            product (Product): 包含出库信息的商品对象
            
        Returns:
            bool: 出库成功返回True，失败返回False
        """
        found_products = self.searchGood(product, print_flag=False)
        
        if not found_products:
            print("错误：未找到要出库的商品")
            return False
        
        # 如果找到多个同名商品，让用户选择
        if len(found_products) > 1 and not product.index:
            print("找到多个同名商品：")
            for i, good in enumerate(found_products, 1):
                print(f"{i}. {good.formatPrint()}")
            
            try:
                choice = int(input("请选择要出库的商品编号: ")) - 1
                if 0 <= choice < len(found_products):
                    target_product = found_products[choice]
                else:
                    print("错误：选择的编号无效")
                    return False
            except ValueError:
                print("错误：请输入有效的数字")
                return False
        else:
            target_product = found_products[0]
        
        # 检查库存是否足够
        if target_product.number < product.number:
            print(f"错误：库存不足。当前库存: {target_product.number}, 请求出库: {product.number}")
            return False
        
        # 执行出库操作
        target_product.number -= product.number
        print(f"商品 {target_product.name} 出库成功，出库数量: {product.number}")
        
        # 如果数量为0，可以选择是否移除商品
        if target_product.number == 0:
            print(f"商品 {target_product.name} 库存为0")
        
        return True

    def listAllGoods(self):
        """
        显示所有商品列表
        """
        if not self.goods:
            print("库存为空")
            return

        print("=== 商品列表 ===")
        print(f"{'序号':<4} {'商品名称':<15} {'价格':<10} {'数量':<8} {'序列号':<15} {'总价值':<10}")
        print("-" * 70)

        for i, good in enumerate(self.goods, 1):
            total_value = good.getTotalValue()
            print(f"{i:<4} {good.name:<15} {good.price:<10.2f} {good.number:<8} {good.index:<15} {total_value:<10.2f}")

    def statisticGoods(self, min_number=15):
        """
        统计商品信息

        Args:
            min_number (int): 最小库存参数，默认为15
        """
        if not self.goods:
            print("库存为空，无法统计")
            return

        # 统计基本信息
        total_types = len(self.goods)
        total_quantity = sum(good.number for good in self.goods)
        total_value = sum(good.getTotalValue() for good in self.goods)

        print("=== 库存统计 ===")
        print(f"商品类型数量: {total_types}")
        print(f"商品总数量: {total_quantity}")
        print(f"商品总价值: {total_value:.2f}")

        # 显示库存不足的商品
        low_stock_goods = [good for good in self.goods if good.number < min_number]

        if low_stock_goods:
            print(f"\n库存不足商品 (少于 {min_number} 件):")
            print(f"{'商品名称':<15} {'当前库存':<10} {'序列号':<15}")
            print("-" * 40)
            for good in low_stock_goods:
                print(f"{good.name:<15} {good.number:<10} {good.index:<15}")
        else:
            print(f"\n所有商品库存充足 (≥ {min_number} 件)")

    def getGoods(self):
        """
        返回商品数据

        Returns:
            list: 商品列表
        """
        return self.goods

    def setGoods(self, goods):
        """
        设置商品数据

        Args:
            goods (list): 商品列表
        """
        if isinstance(goods, list):
            self.goods = goods
            print(f"成功设置 {len(goods)} 个商品")
        else:
            print("错误：输入的不是有效的商品列表")

    def clearStock(self):
        """
        清空库存
        """
        self.goods.clear()
        print("库存已清空")

    def getTotalValue(self):
        """
        获取库存总价值

        Returns:
            float: 库存总价值
        """
        return sum(good.getTotalValue() for good in self.goods)
