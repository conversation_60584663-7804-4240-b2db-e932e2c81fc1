#!/usr/bin/env python3
# -*- coding: utf-8 -*-


# 导入必要的模块
from Product import *    # 导入商品类
from Stock import *     # 导入库存管理类
from xlwt import *      # 导入Excel写入模块
from xlrd import *      # 导入Excel读取模块


class StockIo(object):
    """
    库存数据导入导出类
    负责处理库存数据的文件操作，支持文本文件和Excel文件的读写
    提供数据的持久化存储和恢复功能
    """
    def __init__(self):
        """
        构造函数
        初始化商品列表为空列表
        """
        # 商品列表，私有属性
        self.__goods = []
    
    def export_to_file(self, goods):
        """
        导出商品列表到文本文件
        参数：
        - goods: 要导出的商品列表
        文件格式：GOODS.txt
        数据格式：每行一个商品，格式为"名称,价格,数量,编号"
        """
        # 检查商品列表是否为空
        if len(goods) == 0:
            print("商品列表为空，无法导出")
            return

        # 打开文件并写入数据
        with open(r"GOODS.txt", "w+", encoding="UTF-8") as f:
            for good in goods:
                f.write(f"{good.name},{good.price},{good.number},{good.index}\n")
            f.close()

        print(f"成功导出 {len(goods)} 个商品到 GOODS.txt 文件")
    
    def import_from_file(self, file_path, stock):
        """
        从文本文件导入商品列表
        参数：
        - file_path: 文件路径
        - stock: Stock对象，用于更新库存
        文件格式：GOODS.txt
        数据格式：每行一个商品，格式为"名称,价格,数量,编号"
        """
        goods = []    # 创建临时商品列表
        # 打开文件并读取数据
        try:
            with open(file_path, "r", encoding="UTF-8") as f:
                lines = f.readlines()
                for line in lines:
                    line = line.strip()
                    if line:
                        parts = line.split(',')
                        if len(parts) >= 4:
                            name = parts[0].strip()
                            price = float(parts[1].strip())
                            number = int(parts[2].strip())
                            index = parts[3].strip()

                            product = Product(name, price, number, index)
                            goods.append(product)
                f.close()

            # 更新库存数据
            stock.setGoods(goods)
            print(f"成功从 {file_path} 导入 {len(goods)} 个商品")

        except FileNotFoundError:
            print(f"错误：文件 {file_path} 不存在")
        except Exception as e:
            print(f"导入文件失败: {str(e)}")
    
    def export_to_excel(self, goods):
        """
        导出商品列表到Excel文件
        参数：
        - goods: 要导出的商品列表
        文件格式：GOODS.xls
        表格结构：
        - 表头：名称、价格、数量、编号
        - 数据行：每个商品的信息
        """
        # 创建Excel工作簿
        workbook = Workbook()
        # 创建工作表
        worksheet = workbook.add_sheet('商品列表')
        # 写入表头
        headers = ['名称', '价格', '数量', '编号']
        for col, header in enumerate(headers):
            worksheet.write(0, col, header)

        # 写入商品数据
        for row, good in enumerate(goods, 1):
            worksheet.write(row, 0, good.name)
            worksheet.write(row, 1, good.price)
            worksheet.write(row, 2, good.number)
            worksheet.write(row, 3, good.index)

        # 保存Excel文件
        workbook.save('GOODS.xls')
        print(f"成功导出 {len(goods)} 个商品到 GOODS.xls 文件")

    def import_from_excel(self, file_path, stock):
        """
        从Excel文件导入商品列表
        参数：
        - file_path: Excel文件路径
        - stock: Stock对象，用于更新库存
        文件格式：GOODS.xls
        表格结构：
        - 工作表名：商品列表
        - 列结构：名称、价格、数量、编号
        """
        goods = []    # 创建临时商品列表
        try:
            # 打开Excel文件
            workbook = open_workbook(file_path)
            # 获取工作表
            worksheet = workbook.sheet_by_name('商品列表')

            # 读取商品数据（从第二行开始，跳过表头）
            for row in range(1, worksheet.nrows):
                name = str(worksheet.cell_value(row, 0)).strip()
                price = float(worksheet.cell_value(row, 1))
                number = int(worksheet.cell_value(row, 2))
                index = str(worksheet.cell_value(row, 3)).strip()

                product = Product(name, price, number, index)
                goods.append(product)

            # 更新库存数据
            stock.setGoods(goods)
            print(f"成功从 {file_path} 导入 {len(goods)} 个商品")

        except FileNotFoundError:
            print(f"错误：文件 {file_path} 不存在")
        except Exception as e:
            print(f"导入Excel文件失败: {str(e)}")


