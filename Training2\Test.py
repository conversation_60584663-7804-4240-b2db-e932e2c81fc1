#!/usr/bin/env python3
# -*- coding: utf-8 -*-


from Product import *
from Stock import *
from StockIo import *


# 建立仓库实例
s = Stock()

# 建立io实例
io = StockIo()

# 输入循环
while(True):
    print("操作指南[E: 退出程序，I:商品入库，L：商品列表，S1:库存统计（默认参数）,S2库存统计（设置参数）]")
    print("操作指南[ON:通过商品名出库，OI：通过序列号出库]，SN:通过商品名查询，SI：通过序列号查询]")
    print("操作指南[OF:导出商品列表至文件，IF：从文件导入商品列表,OE:导出商品列表至EXCEL，IE：从EXCEL导入商品列表]")
    key = input("输入指令：")
    if (key == "E" or key == "e"):
        break
    elif(key=="I" or key=="i"):
        name, price, number, index = (input("请输入商品名 价格 数量 序列号：").split())
        s.intoStock(Product(name, float(price), int(number), index))
        s.listAllGoods()
    elif (key == "ON" or key == "on"):
        name, number = (input("请输入商品名 数量：").split())
        p = Product()
        p.setName(name)
        p.setNumber(int(number))
        s.outOfStock(p)
    elif (key == "OI" or key == "oi"):
        index, number = (input("请输入序列号 数量：").split())
        p = Product()
        p.setIndex(index)
        p.setNumber(int(number))
        s.outOfStock(p)
    elif (key == "SN" or key == "sn"):
        name = input("请输入商品名：")
        p = Product()
        p.setName(name)
        s.searchGood(p)
    elif (key == "SI" or key == "si"):
        index = input("请输入序列号：")
        p = Product()
        p.setIndex(index)
        s.searchGood(p)
    elif (key == "L" or key == "l"):
        s.listAllGoods()
    elif (key == "S1" or key == "s1"):
        s.statisticGoods()
    elif (key == "S2" or key == "s2"):
        min_number = input("请输入商品库存下限：")
        s.statisticGoods(int(min_number))
    elif (key == "OF" or key == "of"):
        io.export_to_file(s.getGoods())
    elif (key == "IF" or key == "if"):
        io.import_from_file(r"GOODS.txt", s)
    elif (key == "OE" or key == "oe"):
        io.export_to_excel(s.getGoods())
    elif (key == "IE" or key == "ie"):
        io.import_from_excel(r"GOODS.xls", s)
    
    def input_product_info(self, for_search=False, for_outstock=False):
        """
        输入商品信息
        
        Args:
            for_search (bool): 是否用于查询，默认False
            for_outstock (bool): 是否用于出库，默认False
            
        Returns:
            Product: 商品对象
        """
        try:
            if for_search:
                print("请输入查询信息:")
                name = input("商品名称 (可选): ").strip()
                index = input("商品序列号 (可选): ").strip()
                return Product(name=name, index=index)
            
            elif for_outstock:
                print("请输入出库信息:")
                name = input("商品名称 (可选): ").strip()
                index = input("商品序列号 (可选): ").strip()
                
                while True:
                    try:
                        number = int(input("出库数量: "))
                        if number > 0:
                            break
                        else:
                            print("出库数量必须大于0")
                    except ValueError:
                        print("请输入有效的数字")
                
                return Product(name=name, number=number, index=index)
            
            else:
                print("请输入商品信息:")
                name = input("商品名称: ").strip()
                
                while True:
                    try:
                        price = float(input("商品价格: "))
                        if price >= 0:
                            break
                        else:
                            print("价格不能为负数")
                    except ValueError:
                        print("请输入有效的价格")
                
                while True:
                    try:
                        number = int(input("商品数量: "))
                        if number >= 0:
                            break
                        else:
                            print("数量不能为负数")
                    except ValueError:
                        print("请输入有效的数量")
                
                index = input("商品序列号: ").strip()
                
                return Product(name, price, number, index)
                
        except KeyboardInterrupt:
            print("\n操作已取消")
            return None
    
    def handle_instock(self):
        """
        处理商品入库
        """
        print("\n=== 商品入库 ===")
        product = self.input_product_info()
        if product:
            self.stock.intoStock(product)
    
    def handle_outstock_by_name(self):
        """
        处理通过商品名出库
        """
        print("\n=== 通过商品名出库 ===")
        product = self.input_product_info(for_outstock=True)
        if product and product.name:
            self.stock.outOfStock(product)
        else:
            print("错误：必须提供商品名称")
    
    def handle_outstock_by_index(self):
        """
        处理通过序列号出库
        """
        print("\n=== 通过序列号出库 ===")
        product = self.input_product_info(for_outstock=True)
        if product and product.index:
            self.stock.outOfStock(product)
        else:
            print("错误：必须提供商品序列号")
    
    def handle_search_by_name(self):
        """
        处理通过商品名查询
        """
        print("\n=== 通过商品名查询 ===")
        name = input("请输入商品名称: ").strip()
        if name:
            product = Product(name=name)
            self.stock.searchGood(product)
        else:
            print("错误：商品名称不能为空")
    
    def handle_search_by_index(self):
        """
        处理通过序列号查询
        """
        print("\n=== 通过序列号查询 ===")
        index = input("请输入商品序列号: ").strip()
        if index:
            product = Product(index=index)
            self.stock.searchGood(product)
        else:
            print("错误：商品序列号不能为空")
    
    def handle_list_goods(self):
        """
        处理显示商品列表
        """
        print("\n=== 商品列表 ===")
        self.stock.listAllGoods()
    
    def handle_statistics_default(self):
        """
        处理库存统计（默认参数）
        """
        print("\n=== 库存统计（默认参数）===")
        self.stock.statisticGoods()
    
    def handle_statistics_custom(self):
        """
        处理库存统计（设置参数）
        """
        print("\n=== 库存统计（设置参数）===")
        try:
            min_number = int(input("请输入最小库存参数: "))
            self.stock.statisticGoods(min_number)
        except ValueError:
            print("错误：请输入有效的数字")
    
    def handle_export_file(self):
        """
        处理导出商品列表至文件
        """
        print("\n=== 导出商品列表至文件 ===")
        file_path = input("请输入文件路径 (默认: goods_data.txt): ").strip()
        if not file_path:
            file_path = "goods_data.txt"
        
        goods = self.stock.getGoods()
        self.stock_io.export_to_file(goods, file_path)
    
    def handle_import_file(self):
        """
        处理从文件导入商品列表
        """
        print("\n=== 从文件导入商品列表 ===")
        file_path = input("请输入文件路径: ").strip()
        if file_path:
            self.stock_io.import_from_file(file_path, self.stock)
        else:
            print("错误：文件路径不能为空")
    
    def handle_export_excel(self):
        """
        处理导出商品列表至Excel
        """
        print("\n=== 导出商品列表至EXCEL ===")
        file_path = input("请输入Excel文件路径 (默认: goods_data.xls): ").strip()
        if not file_path:
            file_path = "goods_data.xls"
        
        goods = self.stock.getGoods()
        self.stock_io.export_to_excel(goods, file_path)
    
    def handle_import_excel(self):
        """
        处理从Excel导入商品列表
        """
        print("\n=== 从EXCEL导入商品列表 ===")
        file_path = input("请输入Excel文件路径: ").strip()
        if file_path:
            self.stock_io.import_from_excel(file_path, self.stock)
        else:
            print("错误：文件路径不能为空")

    def run(self):
        """
        运行库存管理系统主循环
        """
        print("欢迎使用库存管理系统！")

        # 命令映射字典
        command_handlers = {
            'E': self.exit_system,
            'I': self.handle_instock,
            'L': self.handle_list_goods,
            'S1': self.handle_statistics_default,
            'S2': self.handle_statistics_custom,
            'ON': self.handle_outstock_by_name,
            'OI': self.handle_outstock_by_index,
            'SN': self.handle_search_by_name,
            'SI': self.handle_search_by_index,
            'OF': self.handle_export_file,
            'IF': self.handle_import_file,
            'OE': self.handle_export_excel,
            'IE': self.handle_import_excel
        }

        while True:
            try:
                self.show_main_menu()
                command = input("\n请输入操作命令: ").strip().upper()

                if command in command_handlers:
                    command_handlers[command]()
                else:
                    print("错误：无效的命令，请重新输入")

                # 等待用户按键继续
                if command != 'E':
                    input("\n按回车键继续...")

            except KeyboardInterrupt:
                print("\n\n程序被用户中断")
                break
            except Exception as e:
                print(f"发生错误: {str(e)}")
                input("按回车键继续...")

    def exit_system(self):
        """
        退出系统
        """
        print("\n感谢使用库存管理系统，再见！")
        exit(0)


def main():
    """
    主函数
    """
    system = StockManagementSystem()
    system.run()


if __name__ == "__main__":
    main()
